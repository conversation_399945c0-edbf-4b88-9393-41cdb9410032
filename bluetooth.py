import serial
import pygame

# 🔸 عدّل هنا البورت اللي مربوط بالبلوتوث عندك
bluetooth_port = 'COM6'  # ويندوز مثال، في لينكس ممكن '/dev/ttyUSB0' أو '/dev/ttyACM0'

# سرعة البود لازم تكون نفس سرعة ESP32 في الكود (115200)
ser = serial.Serial(bluetooth_port, 115200, timeout=1)

# تحميل pygame لتشغيل الأصوات
pygame.init()
sounds = {
    "✋ سلام": pygame.mixer.Sound("salam.wav"),
    "🙋‍♀ أنا": pygame.mixer.Sound("ana.wav"),
    "👉 أنت": pygame.mixer.Sound("anta.wav"),
    "👋 هاي": pygame.mixer.Sound("hi.wav"),
}

print("🎧 جاهز لاستقبال رسائل البلوتوث...")

while True:
    if ser.in_waiting:
        line = ser.readline().decode('utf-8').strip()
        print(f"Received: {line}")
        if line in sounds:
            sounds[line].play()
