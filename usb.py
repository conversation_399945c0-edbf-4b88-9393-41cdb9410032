import serial
import pygame

# 🟡 تأكد من اسم المنفذ (عدّله حسب جهازك)
ser = serial.Serial('COM6', 115200)

# 🔊 تحميل مكتبة الأصوات
pygame.init()
sounds = {
    "✋ سلام": pygame.mixer.Sound("salam.wav"),
    "🙋‍♀ أنا": pygame.mixer.Sound("ana.wav"),
    "👉 أنت": pygame.mixer.Sound("anta.wav"),
    "👋 هاي": pygame.mixer.Sound("hi.wav"),
}

print("🎧 نظام الصوت جاهز... بانتظار ESP32")

while True:
    if ser.in_waiting:
        line = ser.readline().decode().strip()
        print("📢", line)
        if line in sounds:
            sounds[line].play()
